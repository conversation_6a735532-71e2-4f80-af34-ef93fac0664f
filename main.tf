provider "aws" {
  region = "eu-central-1"
}


resource "aws_s3_bucket" "terraform_state" {
  bucket = "hypernative-handler-terraform-state"
}

resource "aws_s3_bucket_versioning" "versioning" {
  bucket = aws_s3_bucket.terraform_state.bucket

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "encryption" {
  bucket = aws_s3_bucket.terraform_state.bucket

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

terraform {
 backend "s3" {
   bucket  = "hypernative-handler-terraform-state"
   key     = "terraform.tfstate"
    region  = "eu-central-1"
    encrypt = true
  }
}

resource "aws_lambda_function" "hypernative_handler" {
  function_name = "hypernative_handler"
  package_type  = "Image"
  role          = "arn:aws:iam::236840575805:role/lambda_exec_role"
  image_uri     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_handler:latest"
  architectures = ["x86_64"]
  timeout       = 300
  memory_size   = 1024
  tags = {
    Name = "hypernative_handler"
  }

  environment {
    variables = {
      cloud_env = "hypernative_handler"
    }
  }
}

resource "aws_api_gateway_rest_api" "hypernative_handler_api" {
  name        = "hypernative_handler_api"
  description = "API Gateway to invoke Lambda function"
}

resource "aws_api_gateway_resource" "hypernative_handler_resource" {
  rest_api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
  parent_id   = aws_api_gateway_rest_api.hypernative_handler_api.root_resource_id
  path_part   = "hypernative_handler"
}

resource "aws_api_gateway_method" "hypernative_handler_method" {
  rest_api_id   = aws_api_gateway_rest_api.hypernative_handler_api.id
  resource_id   = aws_api_gateway_resource.hypernative_handler_resource.id
  http_method   = "POST"
  authorization = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "hypernative_handler_integration" {
  rest_api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
  resource_id = aws_api_gateway_resource.hypernative_handler_resource.id
  http_method = aws_api_gateway_method.hypernative_handler_method.http_method

  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/${"arn:aws:lambda:eu-central-1:236840575805:function:hypernative_handler"}/invocations"
}

resource "aws_api_gateway_deployment" "hypernative_handler_deployment" {
  depends_on = [
    aws_api_gateway_integration.hypernative_handler_integration
  ]

  rest_api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
  stage_name  = "prod"

  # Trigger a new deployment on configuration change
  description = "Deployment on ${timestamp()}"
}

resource "aws_api_gateway_api_key" "hypernative_handler_api_key" {
  name = "hypernative_handler_api_key"
}

resource "aws_api_gateway_usage_plan" "hypernative_handler_usage_plan" {
  name = "hypernative_handler_usage_plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
    stage  = aws_api_gateway_deployment.hypernative_handler_deployment.stage_name
  }

  quota_settings {
    limit  = 1000
    offset = 0
    period = "MONTH"
  }

  throttle_settings {
    burst_limit = 200
    rate_limit  = 100
  }
}

resource "aws_api_gateway_usage_plan_key" "hypernative_handler_usage_plan_key" {
  key_id        = aws_api_gateway_api_key.hypernative_handler_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.hypernative_handler_usage_plan.id
}

resource "aws_lambda_permission" "api_gw" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = "arn:aws:lambda:eu-central-1:236840575805:function:hypernative_handler"
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.hypernative_handler_api.execution_arn}/*/POST/hypernative_handler"
}

# create a cloudwatch log group
resource "aws_cloudwatch_log_group" "hypernative_handler_log_group" {
  name = "/aws/lambda/hypernative_handler"
}

# monitor the logs for word 'error' and send an email notification (<EMAIL>)
resource "aws_cloudwatch_log_metric_filter" "hypernative_handler_error_log_filter" {
  name           = "hypernative_handler_error_log_filter"
  pattern        = "\"error\""
  log_group_name = aws_cloudwatch_log_group.hypernative_handler_log_group.name
  metric_transformation {
    name      = "ErrorOccurrences"
    namespace = "HypernativeHandler"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "hypernative_error_alarm" {
  alarm_name                = "hypernative-error-alarm"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = "1"
  metric_name               = "ErrorOccurrences"
  namespace                 = "HypernativeHandler"
  period                    = 300
  statistic                 = "Sum"
  threshold                 = "1"
  alarm_description         = "This metric monitors log errors"
  actions_enabled           = true
  alarm_actions             = [aws_sns_topic.alerts.arn]
}

resource "aws_sns_topic" "alerts" {
  name = "hypernative_handler_alerts"

}

resource "aws_sns_topic_subscription" "gasan_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "colin_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "jashiel_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.alerts.arn
  policy = data.aws_iam_policy_document.sns_policy.json
}

data "aws_iam_policy_document" "sns_policy" {
  statement {
    actions = ["SNS:Publish"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    resources = [aws_sns_topic.alerts.arn]
  }
}

# output path
output "api_gateway_invoke_url" {
  value = "${aws_api_gateway_deployment.hypernative_handler_deployment.invoke_url}"
}
