provider "aws" {
  region = "eu-central-1"
}


resource "aws_s3_bucket" "terraform_state" {
  bucket = "hypernative-handler-terraform-state"
}

resource "aws_s3_bucket_versioning" "versioning" {
  bucket = aws_s3_bucket.terraform_state.bucket

  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "encryption" {
  bucket = aws_s3_bucket.terraform_state.bucket

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

terraform {
 backend "s3" {
   bucket  = "hypernative-handler-terraform-state"
   key     = "terraform.tfstate"
    region  = "eu-central-1"
    encrypt = true
  }
}

resource "aws_lambda_function" "hypernative_handler" {
  function_name = "hypernative_handler"
  package_type  = "Image"
  role          = "arn:aws:iam::236840575805:role/lambda_exec_role"
  image_uri     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_handler:latest"
  architectures = ["x86_64"]
  timeout       = 300
  memory_size   = 1024
  tags = {
    Name = "hypernative_handler"
  }

  environment {
    variables = {
      cloud_env = "hypernative_handler"
    }
  }
}

resource "aws_api_gateway_rest_api" "hypernative_handler_api" {
  name        = "hypernative_handler_api"
  description = "API Gateway to invoke Lambda function"
}

resource "aws_api_gateway_resource" "hypernative_handler_resource" {
  rest_api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
  parent_id   = aws_api_gateway_rest_api.hypernative_handler_api.root_resource_id
  path_part   = "hypernative_handler"
}

resource "aws_api_gateway_method" "hypernative_handler_method" {
  rest_api_id   = aws_api_gateway_rest_api.hypernative_handler_api.id
  resource_id   = aws_api_gateway_resource.hypernative_handler_resource.id
  http_method   = "POST"
  authorization = "NONE"
  api_key_required = true
}

resource "aws_api_gateway_integration" "hypernative_handler_integration" {
  rest_api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
  resource_id = aws_api_gateway_resource.hypernative_handler_resource.id
  http_method = aws_api_gateway_method.hypernative_handler_method.http_method

  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:eu-central-1:lambda:path/2015-03-31/functions/${"arn:aws:lambda:eu-central-1:236840575805:function:hypernative_handler"}/invocations"
}

resource "aws_api_gateway_deployment" "hypernative_handler_deployment" {
  depends_on = [
    aws_api_gateway_integration.hypernative_handler_integration
  ]

  rest_api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
  stage_name  = "prod"

  # Trigger a new deployment on configuration change
  description = "Deployment on ${timestamp()}"
}

resource "aws_api_gateway_api_key" "hypernative_handler_api_key" {
  name = "hypernative_handler_api_key"
}

resource "aws_api_gateway_usage_plan" "hypernative_handler_usage_plan" {
  name = "hypernative_handler_usage_plan"

  api_stages {
    api_id = aws_api_gateway_rest_api.hypernative_handler_api.id
    stage  = aws_api_gateway_deployment.hypernative_handler_deployment.stage_name
  }

  quota_settings {
    limit  = 1000
    offset = 0
    period = "MONTH"
  }

  throttle_settings {
    burst_limit = 200
    rate_limit  = 100
  }
}

resource "aws_api_gateway_usage_plan_key" "hypernative_handler_usage_plan_key" {
  key_id        = aws_api_gateway_api_key.hypernative_handler_api_key.id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.hypernative_handler_usage_plan.id
}

resource "aws_lambda_permission" "api_gw" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = "arn:aws:lambda:eu-central-1:236840575805:function:hypernative_handler"
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.hypernative_handler_api.execution_arn}/*/POST/hypernative_handler"
}

# create a cloudwatch log group
resource "aws_cloudwatch_log_group" "hypernative_handler_log_group" {
  name = "/aws/lambda/hypernative_handler"
}

# monitor the logs for word 'error' and send an email notification (<EMAIL>)
resource "aws_cloudwatch_log_metric_filter" "hypernative_handler_error_log_filter" {
  name           = "hypernative_handler_error_log_filter"
  pattern        = "\"error\""
  log_group_name = aws_cloudwatch_log_group.hypernative_handler_log_group.name
  metric_transformation {
    name      = "ErrorOccurrences"
    namespace = "HypernativeHandler"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "hypernative_error_alarm" {
  alarm_name                = "hypernative-error-alarm"
  comparison_operator       = "GreaterThanOrEqualToThreshold"
  evaluation_periods        = "1"
  metric_name               = "ErrorOccurrences"
  namespace                 = "HypernativeHandler"
  period                    = 300
  statistic                 = "Sum"
  threshold                 = "1"
  alarm_description         = "This metric monitors log errors"
  actions_enabled           = true
  alarm_actions             = [aws_sns_topic.alerts.arn]
}

resource "aws_sns_topic" "alerts" {
  name = "hypernative_handler_alerts"

}

resource "aws_sns_topic_subscription" "gasan_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "colin_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_subscription" "jashiel_email" {
  topic_arn = aws_sns_topic.alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}

resource "aws_sns_topic_policy" "default" {
  arn    = aws_sns_topic.alerts.arn
  policy = data.aws_iam_policy_document.sns_policy.json
}

data "aws_iam_policy_document" "sns_policy" {
  statement {
    actions = ["SNS:Publish"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["cloudwatch.amazonaws.com"]
    }
    resources = [aws_sns_topic.alerts.arn]
  }
}

# SQS Queue for asynchronous agent processing
resource "aws_sqs_queue" "hypernative_agent_processing" {
  name                      = "hypernative-agent-processing"
  delay_seconds             = 0
  max_message_size          = 262144
  message_retention_seconds = 1209600  # 14 days
  receive_wait_time_seconds = 0
  visibility_timeout_seconds = 300     # Match Lambda timeout

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.hypernative_agent_processing_dlq.arn
    maxReceiveCount     = 3
  })

  tags = {
    Name = "hypernative-agent-processing"
  }
}

# Dead Letter Queue for failed messages
resource "aws_sqs_queue" "hypernative_agent_processing_dlq" {
  name                      = "hypernative-agent-processing-dlq"
  message_retention_seconds = 1209600  # 14 days

  tags = {
    Name = "hypernative-agent-processing-dlq"
  }
}

# DynamoDB table for idempotency
resource "aws_dynamodb_table" "hypernative_idempotency" {
  name           = "hypernative-idempotency"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "idempotency_key"

  attribute {
    name = "idempotency_key"
    type = "S"
  }

  ttl {
    attribute_name = "ttl"
    enabled        = true
  }

  tags = {
    Name = "hypernative-idempotency"
  }
}

# Worker Lambda function
resource "aws_lambda_function" "hypernative_worker" {
  function_name = "hypernative_worker"
  package_type  = "Image"
  role          = "arn:aws:iam::236840575805:role/lambda_exec_role"
  image_uri     = "236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_worker:latest"
  architectures = ["x86_64"]
  timeout       = 300
  memory_size   = 1024

  tags = {
    Name = "hypernative_worker"
  }

  environment {
    variables = {
      cloud_env = "hypernative_handler"
    }
  }
}

# Event source mapping for SQS to Lambda
resource "aws_lambda_event_source_mapping" "hypernative_worker_sqs" {
  event_source_arn = aws_sqs_queue.hypernative_agent_processing.arn
  function_name    = aws_lambda_function.hypernative_worker.arn
  batch_size       = 1  # Process one message at a time for better error handling
  enabled          = true
}

# CloudWatch log group for worker
resource "aws_cloudwatch_log_group" "hypernative_worker_log_group" {
  name = "/aws/lambda/hypernative_worker"
}

# IAM policy for SQS and DynamoDB access
resource "aws_iam_policy" "hypernative_sqs_dynamodb_policy" {
  name        = "hypernative-sqs-dynamodb-policy"
  description = "Policy for Lambda functions to access SQS and DynamoDB"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "sqs:SendMessage",
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:GetQueueAttributes"
        ]
        Resource = [
          aws_sqs_queue.hypernative_agent_processing.arn,
          aws_sqs_queue.hypernative_agent_processing_dlq.arn
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynamodb:DeleteItem",
          "dynamodb:UpdateItem"
        ]
        Resource = aws_dynamodb_table.hypernative_idempotency.arn
      }
    ]
  })
}

# Attach the policy to the existing lambda execution role
resource "aws_iam_role_policy_attachment" "hypernative_sqs_dynamodb_attachment" {
  role       = "lambda_exec_role"
  policy_arn = aws_iam_policy.hypernative_sqs_dynamodb_policy.arn
}

# ECR repository for worker Lambda (if it doesn't exist)
resource "aws_ecr_repository" "hypernative_worker" {
  name                 = "hypernative_worker"
  image_tag_mutability = "MUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }

  tags = {
    Name = "hypernative_worker"
  }
}

# output path
output "api_gateway_invoke_url" {
  value = "${aws_api_gateway_deployment.hypernative_handler_deployment.invoke_url}"
}

output "sqs_queue_url" {
  value = aws_sqs_queue.hypernative_agent_processing.url
}
