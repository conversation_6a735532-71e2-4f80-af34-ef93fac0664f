from risk_index import risk_index
from custom_agents.agent import Agent
import json
import boto3
from botocore.exceptions import ClientError

from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

from helpers.log import get_logger

logger = get_logger(__name__)


def process_agent_execution(event):
    """Process the agent execution logic from the original handler."""
    try:
        # Extract the data from the event to detect the Custom Agent needed
        body = json.loads(event["body"])
        data = json.loads(body["data"])
        watchlists = data["watchlists"]
        
        # Process custom agents
        if "customAgents" in data:
            custom_agents = data["customAgents"]
            if custom_agents != []:
                for custom_agent in custom_agents:
                    agent_name = custom_agent["agentName"]
                    if agent_name in risk_index:
                        logger.info(f"Custom Agent detected: {agent_name}")
                        custom_agent_class = risk_index[agent_name]
                        if isinstance(custom_agent_class, list):
                            for agent_class in custom_agent_class:
                                try:
                                    custom_agent_instance: Agent = agent_class()
                                    logger.info(
                                        f"Executing Custom Agent: {custom_agent_instance.__class__.__name__}"
                                    )
                                    custom_agent_instance.execute()
                                except Exception as e:
                                    logger.error(
                                        f"Error executing agent {agent_class.__name__}: {e}"
                                    )
                        else:
                            custom_agent_instance: Agent = custom_agent_class()
                            logger.info(
                                f"Executing Custom Agent: {custom_agent_instance.__class__.__name__}"
                            )
                            custom_agent_instance.execute()

        # Process watchlists
        if watchlists != []:
            for watchlist in watchlists:
                watchlist_name = watchlist["name"]
                if watchlist_name in risk_index:
                    logger.info(f"Watchlist detected: {watchlist_name}")

                    assets = watchlist["assets"]
                    if assets != []:
                        for asset in assets:
                            risk_chain = asset["chain"]
                            if risk_chain in risk_index[watchlist_name]:
                                logger.info(f"Chain detected: {risk_chain}")
                                custom_agent_class = risk_index[watchlist_name][
                                    risk_chain
                                ]
                                if isinstance(custom_agent_class, list):
                                    for agent_class in custom_agent_class:
                                        try:
                                            custom_agent: Agent = agent_class()
                                            logger.info(
                                                f"Executing Custom Agent: {custom_agent.__class__.__name__}"
                                            )
                                            custom_agent.execute()
                                        except Exception as e:
                                            logger.error(
                                                f"Error executing agent {agent_class.__name__}: {e}"
                                            )
                                else:
                                    custom_agent: Agent = custom_agent_class()
                                    logger.info(
                                        f"Executing Custom Agent: {custom_agent.__class__.__name__}"
                                    )
                                    custom_agent.execute()
                                logger.info(
                                    f"Risk chain detected: {watchlist_name}"
                                )
        
        logger.info("Agent processing completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error in agent processing: {e}")
        return False


def lambda_handler(event, context):
    """Worker Lambda that processes SQS messages containing webhook events."""
    logger.info(f"Worker received SQS event with {len(event.get('Records', []))} records")
    
    successful_records = 0
    failed_records = 0
    
    for record in event.get('Records', []):
        try:
            # Parse the SQS message body (which contains the original webhook event)
            webhook_event = json.loads(record['body'])
            message_id = record.get('messageId', 'unknown')
            
            logger.info(f"Processing webhook event from SQS message {message_id}")
            
            # Process the webhook event using the original logic
            if process_agent_execution(webhook_event):
                successful_records += 1
                logger.info(f"Successfully processed message {message_id}")
            else:
                failed_records += 1
                logger.error(f"Failed to process message {message_id}")
                
        except Exception as e:
            failed_records += 1
            logger.error(f"Error processing SQS record: {e}")
    
    logger.info(f"Worker completed: {successful_records} successful, {failed_records} failed")
    
    # If any records failed, raise an exception to trigger SQS retry/DLQ behavior
    if failed_records > 0:
        raise Exception(f"Failed to process {failed_records} out of {len(event.get('Records', []))} records")
    
    return {
        'statusCode': 200,
        'body': json.dumps({
            'processed': successful_records,
            'failed': failed_records
        })
    }
