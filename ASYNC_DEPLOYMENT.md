# Asynchronous Lambda Architecture Deployment Guide

This document describes the new asynchronous architecture implemented to prevent API Gateway timeout issues.

## Architecture Overview

The system now consists of two Lambda functions:

1. **Main Handler (`hypernative_handler`)**: Fast webhook receiver that returns within 1-2 seconds
2. **Worker (`hypernative_worker`)**: Processes agent execution asynchronously via SQS

## Components Added

### Infrastructure
- **SQS Queue**: `hypernative-agent-processing` for message queuing
- **Dead Letter Queue**: `hypernative-agent-processing-dlq` for failed messages
- **DynamoDB Table**: `hypernative-idempotency` for duplicate prevention
- **Worker Lambda**: `hypernative_worker` for async processing
- **IAM Policies**: SQS and DynamoDB permissions

### Code Changes
- **hypernative_handler.py**: Modified to queue events and return quickly
- **hypernative_worker.py**: New worker function with original agent logic
- **Dockerfile.worker**: Docker configuration for worker <PERSON><PERSON>
- **deploy_worker.sh**: Deployment script for worker <PERSON>da

## Deployment Steps

### 1. Deploy Infrastructure
```bash
# Apply Terraform changes to create SQS, DynamoDB, and worker Lambda
terraform plan
terraform apply
```

### 2. Build and Deploy Worker Lambda
```bash
# Make deployment script executable
chmod +x ./deploy_worker.sh

# Deploy worker Lambda
./deploy_worker.sh
```

### 3. Deploy Updated Main Handler
```bash
# Deploy main handler with async changes
./deploy.sh
```

### 4. Verify Deployment
```bash
# Check that all resources are created
aws sqs list-queues --queue-name-prefix hypernative
aws dynamodb list-tables | grep hypernative
aws lambda list-functions | grep hypernative
```

## How It Works

### Request Flow
1. Webhook arrives at API Gateway → Main Handler
2. Main Handler:
   - Generates idempotency key
   - Checks DynamoDB for duplicates
   - Handles ForDefi transactions synchronously (fast)
   - Queues agent processing to SQS
   - Returns 200 within 1-2 seconds
3. SQS triggers Worker Lambda
4. Worker Lambda executes agents (can take minutes)

### Idempotency
- Uses `fordefi-transaction-id` when available
- Falls back to SHA256 hash of event body
- 24-hour TTL prevents indefinite storage
- Conditional DynamoDB writes prevent duplicates

### Error Handling
- Failed SQS messages retry up to 3 times
- After 3 failures, messages go to Dead Letter Queue
- Worker Lambda logs all errors for monitoring
- Main handler removes idempotency record if queueing fails

## Monitoring

### CloudWatch Logs
- `/aws/lambda/hypernative_handler`: Main handler logs
- `/aws/lambda/hypernative_worker`: Worker processing logs

### Key Metrics to Monitor
- API Gateway response times (should be <2 seconds)
- SQS queue depth and age
- DynamoDB throttling
- Lambda error rates and durations

### Alarms
- Existing error alarms continue to work
- Consider adding SQS queue depth alarms
- Monitor Dead Letter Queue for failed processing

## Configuration

### Environment Variables
Both Lambda functions use the same environment:
- `cloud_env=hypernative_handler`

### Queue Configuration
- **Visibility Timeout**: 300 seconds (matches Lambda timeout)
- **Message Retention**: 14 days
- **Max Receive Count**: 3 (before DLQ)
- **Batch Size**: 1 (process one message at a time)

## Testing

### Test Fast Response
```bash
# Should return within 1-2 seconds
curl -X POST \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"body": "{\"data\": \"{\\\"customAgents\\\": [], \\\"watchlists\\\": []}\"}"}' \
  https://YOUR_API_GATEWAY_URL/prod/hypernative_handler
```

### Test Idempotency
```bash
# Send same request twice - should process only once
# Check DynamoDB table for idempotency records
aws dynamodb scan --table-name hypernative-idempotency
```

### Monitor Processing
```bash
# Check SQS queue
aws sqs get-queue-attributes \
  --queue-url https://sqs.eu-central-1.amazonaws.com/236840575805/hypernative-agent-processing \
  --attribute-names All

# Check worker logs
aws logs tail /aws/lambda/hypernative_worker --follow
```

## Rollback Plan

If issues occur, you can rollback by:

1. **Revert main handler**: Deploy previous version without SQS integration
2. **Disable worker**: Set SQS event source mapping enabled=false
3. **Emergency**: Manually process queued messages or purge queue

## Performance Expectations

- **API Response Time**: <2 seconds (vs previous >30 seconds)
- **Agent Processing**: Unchanged (still takes minutes for blockchain operations)
- **Throughput**: Higher due to async processing
- **Reliability**: Better due to retry mechanisms and DLQ

## Troubleshooting

### Common Issues
1. **IAM Permissions**: Ensure lambda_exec_role has SQS/DynamoDB permissions
2. **Queue URL**: Verify hardcoded queue URL matches actual queue
3. **ECR Repository**: Ensure hypernative_worker ECR repo exists
4. **Network**: Worker may need VPC config if accessing private resources

### Debug Commands
```bash
# Check IAM permissions
aws iam list-attached-role-policies --role-name lambda_exec_role

# Test SQS access
aws sqs send-message \
  --queue-url https://sqs.eu-central-1.amazonaws.com/236840575805/hypernative-agent-processing \
  --message-body '{"test": "message"}'

# Check DynamoDB access
aws dynamodb put-item \
  --table-name hypernative-idempotency \
  --item '{"idempotency_key": {"S": "test"}, "timestamp": {"N": "1640995200"}}'
```
