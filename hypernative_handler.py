from helpers.sign_trigger_transaction import trigger_transaction_signing
from risk_index import risk_index
from custom_agents.agent import Agent
import json

from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

from helpers.log import get_logger

logger = get_logger(__name__)


def lambda_handler(event, context):
    # Parse the incoming webhook request
    try:
        logger.info(f"Received webhook event: {event}")

        if (
            "fordefi-transaction-id" in event["headers"]
        ):  # If the webhook includes the ForDefi transaction ID
            # Trigger the transaction signing process
            trigger_transaction_signing(event)
        else:  # If the webhook does not include the ForDefi transaction ID
            # Extract the data from the event to detect the Custom Agent needed
            body = json.loads(event["body"])
            data = json.loads(body["data"])
            watchlists = data["watchlists"]
            if "customAgents" in data:
                custom_agents = data["customAgents"]
                if custom_agents != []:
                    for custom_agent in custom_agents:
                        agent_name = custom_agent["agentName"]
                        if agent_name in risk_index:
                            logger.info(f"Custom Agent detected: {agent_name}")
                            custom_agent_class = risk_index[agent_name]
                            if isinstance(custom_agent_class, list):
                                for agent_class in custom_agent_class:
                                    try:
                                        custom_agent_instance: Agent = agent_class()
                                        logger.info(
                                            f"Executing Custom Agent: {custom_agent_instance.__class__.__name__}"
                                        )
                                        custom_agent_instance.execute()
                                    except Exception as e:
                                        logger.error(
                                            f"Error executing agent {agent_class.__name__}: {e}"
                                        )
                            else:
                                custom_agent_instance: Agent = custom_agent_class()
                                logger.info(
                                    f"Executing Custom Agent: {custom_agent_instance.__class__.__name__}"
                                )
                                custom_agent_instance.execute()

            if watchlists != []:
                for watchlist in watchlists:
                    watchlist_name = watchlist["name"]
                    if watchlist_name in risk_index:
                        logger.info(f"Watchlist detected: {watchlist_name}")

                        assets = watchlist["assets"]
                        if assets != []:
                            for asset in assets:
                                risk_chain = asset["chain"]
                                if risk_chain in risk_index[watchlist_name]:
                                    logger.info(f"Chain detected: {risk_chain}")
                                    custom_agent_class = risk_index[watchlist_name][
                                        risk_chain
                                    ]
                                    if isinstance(custom_agent_class, list):
                                        for agent_class in custom_agent_class:
                                            try:
                                                custom_agent: Agent = agent_class()
                                                logger.info(
                                                    f"Executing Custom Agent: {custom_agent.__class__.__name__}"
                                                )
                                                custom_agent.execute()
                                            except Exception as e:
                                                logger.error(
                                                    f"Error executing agent {agent_class.__name__}: {e}"
                                                )
                                    else:
                                        custom_agent: Agent = custom_agent_class()
                                        logger.info(
                                            f"Executing Custom Agent: {custom_agent.__class__.__name__}"
                                        )
                                        custom_agent.execute()
                                    logger.info(
                                        f"Risk chain detected: {watchlist_name}"
                                    )

        return {"statusCode": 200, "body": json.dumps("Webhook received successfully")}
    except Exception as e:
        print(f"Error processing webhook: {e}")
        return {"statusCode": 500, "body": json.dumps("Internal Server Error")}
