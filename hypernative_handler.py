from helpers.sign_trigger_transaction import trigger_transaction_signing
from risk_index import risk_index
from custom_agents.agent import Agent
import json
import boto3
import hashlib
import time
from botocore.exceptions import ClientError

from dotenv import load_dotenv

load_dotenv()

from helpers.load_cloud_env import load_cloud_env

load_cloud_env()

from helpers.log import get_logger

logger = get_logger(__name__)

# Initialize AWS clients
sqs = boto3.client('sqs')
dynamodb = boto3.resource('dynamodb')

# Environment variables for queue and table names
QUEUE_URL = "https://sqs.eu-central-1.amazonaws.com/236840575805/hypernative-agent-processing"
IDEMPOTENCY_TABLE = "hypernative-idempotency"


def generate_idempotency_key(event):
    """Generate a unique idempotency key from the event."""
    # Use fordefi-transaction-id if available, otherwise hash the event body
    headers = event.get("headers", {})
    if "fordefi-transaction-id" in headers:
        return f"fordefi-{headers['fordefi-transaction-id']}"

    # For other events, create hash from body content
    body = event.get("body", "")
    event_hash = hashlib.sha256(body.encode()).hexdigest()[:16]
    return f"webhook-{event_hash}"


def check_and_set_idempotency(idempotency_key):
    """Check if event was already processed and mark as processing if not."""
    try:
        table = dynamodb.Table(IDEMPOTENCY_TABLE)

        # Try to put item with condition that it doesn't exist
        table.put_item(
            Item={
                'idempotency_key': idempotency_key,
                'timestamp': int(time.time()),
                'ttl': int(time.time()) + 86400  # 24 hour TTL
            },
            ConditionExpression='attribute_not_exists(idempotency_key)'
        )
        return True  # Successfully set, event is new
    except ClientError as e:
        if e.response['Error']['Code'] == 'ConditionalCheckFailedException':
            return False  # Event already processed
        raise  # Other error, re-raise


def enqueue_for_processing(event):
    """Send event to SQS for asynchronous processing."""
    try:
        response = sqs.send_message(
            QueueUrl=QUEUE_URL,
            MessageBody=json.dumps(event),
            MessageAttributes={
                'EventType': {
                    'StringValue': 'webhook_processing',
                    'DataType': 'String'
                }
            }
        )
        logger.info(f"Event queued for processing: {response['MessageId']}")
        return True
    except Exception as e:
        logger.error(f"Failed to queue event: {e}")
        return False


def lambda_handler(event, context):
    """Fast webhook handler that immediately acknowledges and queues for processing."""
    start_time = time.time()

    try:
        logger.info(f"Received webhook event at {start_time}")

        # Safely extract headers and body
        headers = event.get("headers", {})
        body = event.get("body")

        if not body:
            logger.warning("No body in webhook event")
            return {"statusCode": 200, "body": json.dumps("No-op: empty body")}

        # Generate idempotency key
        idempotency_key = generate_idempotency_key(event)
        logger.info(f"Processing event with idempotency key: {idempotency_key}")

        # Check if already processed
        if not check_and_set_idempotency(idempotency_key):
            logger.info(f"Event {idempotency_key} already processed, skipping")
            return {"statusCode": 200, "body": json.dumps("Event already processed")}

        # Handle ForDefi transaction signing synchronously (these are typically fast)
        if "fordefi-transaction-id" in headers:
            logger.info("Processing ForDefi transaction signing")
            trigger_transaction_signing(event)
            processing_time = time.time() - start_time
            logger.info(f"ForDefi processing completed in {processing_time:.2f}s")
            return {"statusCode": 200, "body": json.dumps("ForDefi transaction processed")}

        # For agent processing, enqueue for async processing
        if enqueue_for_processing(event):
            processing_time = time.time() - start_time
            logger.info(f"Event queued successfully in {processing_time:.2f}s")
            return {"statusCode": 200, "body": json.dumps("Webhook queued for processing")}
        else:
            # If queueing fails, remove idempotency record to allow retry
            try:
                table = dynamodb.Table(IDEMPOTENCY_TABLE)
                table.delete_item(Key={'idempotency_key': idempotency_key})
            except Exception:
                pass  # Best effort cleanup

            return {"statusCode": 500, "body": json.dumps("Failed to queue for processing")}

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Error in webhook handler after {processing_time:.2f}s: {e}")
        return {"statusCode": 500, "body": json.dumps("Internal Server Error")}
