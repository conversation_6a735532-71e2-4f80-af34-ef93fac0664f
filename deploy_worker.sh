#!/bin/bash

export DOCKER_DEFAULT_PLATFORM=linux/amd64
aws ecr get-login-password --region eu-central-1 | docker login --username AWS --password-stdin 236840575805.dkr.ecr.eu-central-1.amazonaws.com

# Create and use a new builder instance
docker buildx create --use --name mybuilder-worker
docker buildx inspect mybuilder-worker --bootstrap

# Build the Docker image with the correct platform and format
docker buildx build --platform linux/amd64 --output "type=docker" -f Dockerfile.worker -t hypernative_worker .

# Tag and push the Docker image
docker tag hypernative_worker:latest 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_worker
docker push 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_worker

# Update the AWS Lambda function with the new image URI
aws lambda update-function-code --function-name hypernative_worker --image-uri 236840575805.dkr.ecr.eu-central-1.amazonaws.com/hypernative_worker:latest

# Remove the builder instance
docker buildx rm mybuilder-worker
