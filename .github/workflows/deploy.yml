name: Deploy to AWS Lambda

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      deploy_type:
        description: 'Deploy main handler, worker, simulator, or all'
        required: true
        default: 'all'
        type: 'choice'
        options:
          - main
          - worker
          - simulator
          - all

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Deploy Main Handler
        if: ${{ github.event.inputs.deploy_type == 'main' || github.event.inputs.deploy_type == 'all' || !github.event.inputs.deploy_type }}
        run: |
          chmod +x ./deploy.sh
          ./deploy.sh

      - name: Deploy Worker
        if: ${{ github.event.inputs.deploy_type == 'worker' || github.event.inputs.deploy_type == 'all' || !github.event.inputs.deploy_type }}
        run: |
          chmod +x ./deploy_worker.sh
          ./deploy_worker.sh

      - name: Deploy Simulator
        if: ${{ github.event.inputs.deploy_type == 'simulator' || github.event.inputs.deploy_type == 'all' || !github.event.inputs.deploy_type }}
        run: |
          chmod +x ./deploy_simulator.sh
          ./deploy_simulator.sh
