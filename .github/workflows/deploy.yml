name: Deploy to AWS Lambda

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      deploy_type:
        description: 'Deploy main handler, simulator, or both'
        required: true
        default: 'both'
        type: 'choice'
        options:
          - main
          - simulator
          - both

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Deploy Main Handler
        if: ${{ github.event.inputs.deploy_type == 'main' || github.event.inputs.deploy_type == 'both' || !github.event.inputs.deploy_type }}
        run: |
          chmod +x ./deploy.sh
          ./deploy.sh
      
      - name: Deploy Simulator
        if: ${{ github.event.inputs.deploy_type == 'simulator' || github.event.inputs.deploy_type == 'both' || !github.event.inputs.deploy_type }}
        run: |
          chmod +x ./deploy_simulator.sh
          ./deploy_simulator.sh
